#!/usr/bin/env python3
"""
Test Script for E3 Terminal Pin Name Automation

This script tests the terminal pin name automation functionality by connecting to E3.series
and performing basic validation checks without making any changes to the project.

Usage:
1. Open an E3.series project
2. Run this script to test the connection and validate the setup
3. If successful, run the main set_terminal_pin_names.py script

Author: E3 Automation Tools
Date: January 2025
"""

import logging
import sys
import e3series

class TerminalPinNameTester:
    def __init__(self):
        self.app = None
        self.job = None
        self.device = None
        self.pin = None
        self.net_segment = None
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('test_terminal_pin_names.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
    def connect_to_e3(self):
        """Connect to the open E3 application"""
        try:
            # Connect to the active E3.series application
            self.app = e3series.Application()
            self.job = self.app.CreateJobObject()
            self.device = self.job.CreateDeviceObject()
            self.pin = self.job.CreatePinObject()
            self.net_segment = self.job.CreateNetSegmentObject()
            logging.info("Successfully connected to E3 application using e3series library")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to E3: {e}")
            return False
    
    def test_device_detection(self):
        """Test terminal device detection"""
        try:
            logging.info("Testing terminal device detection...")
            
            # Get all device IDs
            device_ids_result = self.job.GetAllDeviceIds()
            
            if not device_ids_result or len(device_ids_result) < 2:
                logging.warning("No devices found in project")
                return False
            
            device_count = device_ids_result[0]
            device_ids = device_ids_result[1]
            
            logging.info(f"Found {device_count} total devices in project")
            
            if device_count == 0:
                logging.warning("No devices found in project")
                return False
            
            # Convert to list if single device
            if not isinstance(device_ids, tuple):
                device_ids = [device_ids] if device_ids is not None else []
            else:
                device_ids = [did for did in device_ids if did is not None]
            
            # Test terminal detection on first few devices
            terminal_count = 0
            test_count = min(5, len(device_ids))  # Test first 5 devices
            
            for i, device_id in enumerate(device_ids[:test_count]):
                try:
                    self.device.SetId(device_id)
                    device_name = self.device.GetName()
                    
                    # Test terminal detection methods
                    is_terminal = self.device.IsTerminal()
                    is_terminal_block = self.device.IsTerminalBlock()
                    
                    is_terminal_device = (is_terminal == 1) or (is_terminal_block == 1)
                    
                    if is_terminal_device:
                        terminal_count += 1
                        logging.info(f"Device {device_name} ({device_id}) is a terminal device (IsTerminal={is_terminal}, IsTerminalBlock={is_terminal_block})")
                    else:
                        logging.debug(f"Device {device_name} ({device_id}) is not a terminal device")
                        
                except Exception as e:
                    logging.error(f"Error testing device {device_id}: {e}")
            
            logging.info(f"Found {terminal_count} terminal devices in first {test_count} devices tested")
            return True
            
        except Exception as e:
            logging.error(f"Error in test_device_detection: {e}")
            return False
    
    def test_pin_operations(self):
        """Test pin operations on a sample device"""
        try:
            logging.info("Testing pin operations...")
            
            # Get all device IDs
            device_ids_result = self.job.GetAllDeviceIds()
            
            if not device_ids_result or len(device_ids_result) < 2:
                logging.warning("No devices found for pin testing")
                return False
            
            device_count = device_ids_result[0]
            device_ids = device_ids_result[1]
            
            if device_count == 0:
                return False
            
            # Convert to list if single device
            if not isinstance(device_ids, tuple):
                device_ids = [device_ids] if device_ids is not None else []
            else:
                device_ids = [did for did in device_ids if did is not None]
            
            # Test pin operations on first device with pins
            for device_id in device_ids[:3]:  # Test first 3 devices
                try:
                    self.device.SetId(device_id)
                    device_name = self.device.GetName()
                    
                    # Get pins for this device
                    pin_ids_result = self.device.GetPinIds()
                    
                    if not pin_ids_result or len(pin_ids_result) < 2:
                        continue
                    
                    pin_count = pin_ids_result[0]
                    pin_ids = pin_ids_result[1]
                    
                    if pin_count == 0:
                        continue
                    
                    # Convert to list if single pin
                    if not isinstance(pin_ids, tuple):
                        pin_ids = [pin_ids] if pin_ids is not None else []
                    else:
                        pin_ids = [pid for pid in pin_ids if pid is not None]
                    
                    logging.info(f"Device {device_name} ({device_id}) has {len(pin_ids)} pins")
                    
                    # Test operations on first pin
                    if pin_ids:
                        pin_id = pin_ids[0]
                        self.pin.SetId(pin_id)
                        
                        # Test getting pin name
                        pin_name = self.pin.GetName()
                        logging.info(f"First pin ({pin_id}) name: '{pin_name}'")
                        
                        # Test getting net segments
                        net_segment_ids_result = self.pin.GetNetSegmentIds()
                        
                        if net_segment_ids_result and len(net_segment_ids_result) >= 2:
                            net_segment_count = net_segment_ids_result[0]
                            logging.info(f"Pin {pin_id} has {net_segment_count} connected net segments")
                            
                            if net_segment_count > 0:
                                net_segment_ids = net_segment_ids_result[1]
                                
                                # Convert to list if single net segment
                                if not isinstance(net_segment_ids, tuple):
                                    net_segment_ids = [net_segment_ids] if net_segment_ids is not None else []
                                else:
                                    net_segment_ids = [nsid for nsid in net_segment_ids if nsid is not None]
                                
                                # Test getting wire number from first net segment
                                if net_segment_ids:
                                    net_segment_id = net_segment_ids[0]
                                    self.net_segment.SetId(net_segment_id)
                                    wire_number = self.net_segment.GetAttributeValue("Wire number")
                                    
                                    if wire_number and wire_number.strip():
                                        logging.info(f"Net segment {net_segment_id} has wire number: '{wire_number}'")
                                    else:
                                        logging.info(f"Net segment {net_segment_id} has no wire number")
                        
                        return True  # Successfully tested pin operations
                        
                except Exception as e:
                    logging.error(f"Error testing pins for device {device_id}: {e}")
                    continue
            
            logging.warning("No devices with pins found for testing")
            return False
            
        except Exception as e:
            logging.error(f"Error in test_pin_operations: {e}")
            return False
    
    def test_wire_numbers(self):
        """Test wire number availability in the project"""
        try:
            logging.info("Testing wire number availability...")
            
            # Get all net segment IDs
            net_segment_ids_result = self.job.GetAllNetSegmentIds()
            
            if not net_segment_ids_result or len(net_segment_ids_result) < 2:
                logging.warning("No net segments found in project")
                return False
            
            net_segment_count = net_segment_ids_result[0]
            net_segment_ids = net_segment_ids_result[1]
            
            logging.info(f"Found {net_segment_count} net segments in project")
            
            if net_segment_count == 0:
                return False
            
            # Convert to list if single net segment
            if not isinstance(net_segment_ids, tuple):
                net_segment_ids = [net_segment_ids] if net_segment_ids is not None else []
            else:
                net_segment_ids = [nsid for nsid in net_segment_ids if nsid is not None]
            
            # Test wire numbers on first few net segments
            wire_number_count = 0
            test_count = min(10, len(net_segment_ids))  # Test first 10 net segments
            
            for net_segment_id in net_segment_ids[:test_count]:
                try:
                    self.net_segment.SetId(net_segment_id)
                    wire_number = self.net_segment.GetAttributeValue("Wire number")
                    
                    if wire_number and wire_number.strip():
                        wire_number_count += 1
                        logging.debug(f"Net segment {net_segment_id} has wire number: '{wire_number}'")
                    else:
                        logging.debug(f"Net segment {net_segment_id} has no wire number")
                        
                except Exception as e:
                    logging.error(f"Error testing net segment {net_segment_id}: {e}")
            
            logging.info(f"Found {wire_number_count} net segments with wire numbers out of {test_count} tested")
            
            if wire_number_count == 0:
                logging.warning("No wire numbers found! Run the wire numbering script first.")
                return False
            
            return True
            
        except Exception as e:
            logging.error(f"Error in test_wire_numbers: {e}")
            return False
    
    def run_tests(self):
        """Run all tests"""
        try:
            logging.info("Starting E3 Terminal Pin Name Automation Tests")
            
            if not self.connect_to_e3():
                logging.error("Failed to connect to E3 application")
                return False
            
            tests_passed = 0
            total_tests = 3
            
            # Test 1: Device detection
            if self.test_device_detection():
                tests_passed += 1
                logging.info("✓ Device detection test passed")
            else:
                logging.error("✗ Device detection test failed")
            
            # Test 2: Pin operations
            if self.test_pin_operations():
                tests_passed += 1
                logging.info("✓ Pin operations test passed")
            else:
                logging.error("✗ Pin operations test failed")
            
            # Test 3: Wire numbers
            if self.test_wire_numbers():
                tests_passed += 1
                logging.info("✓ Wire number test passed")
            else:
                logging.error("✗ Wire number test failed")
            
            logging.info(f"Test Results: {tests_passed}/{total_tests} tests passed")
            
            if tests_passed == total_tests:
                logging.info("All tests passed! The terminal pin name automation should work correctly.")
                return True
            else:
                logging.warning(f"Some tests failed. Check the issues before running the main script.")
                return False
            
        except Exception as e:
            logging.error(f"Error in run_tests: {e}")
            return False

def main():
    """Main entry point"""
    tester = TerminalPinNameTester()
    success = tester.run_tests()
    
    if success:
        print("All tests passed! The terminal pin name automation should work correctly.")
        print("You can now run the main script: set_terminal_pin_names.py")
    else:
        print("Some tests failed. Check the log file for details.")
        print("Resolve any issues before running the main script.")
    
    print("Check the log file 'test_terminal_pin_names.log' for detailed information.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
